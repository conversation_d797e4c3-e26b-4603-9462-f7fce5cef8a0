import os
import sys
import webbrowser
import time
import threading
from pathlib import Path

def main():
    """Main launcher function"""
    print("=" * 60)
    print("🚀 QR ID Desktop Application Launcher")
    print("=" * 60)
    
    # Start browser opening in background
    def open_browser_delayed(port):
        """Open browser after Flask starts"""
        time.sleep(2)  # Wait for Flask to fully start
        webbrowser.open(f'http://localhost:{port}')
    
    # Try different ports if default is in use
    port = 5000
    max_port_attempts = 3
    
    for attempt in range(max_port_attempts):
        try:
            # Start browser thread with current port
            browser_thread = threading.Thread(
                target=open_browser_delayed, 
                args=(port,), 
                daemon=True
            )
            browser_thread.start()
            
            # Start the Flask application
            from app import app
            print(f"Starting server on port {port}...")
            app.run(
                host='127.0.0.1',  # Local only
                port=port,
                debug=False,
                threaded=True,
                use_reloader=False  # Disable reloader for desktop use
            )
            break  # If we get here, server started successfully
            
        except OSError as e:
            if "Address already in use" in str(e) and attempt < max_port_attempts - 1:
                port += 1  # Try next port
                print(f"Port {port-1} in use, trying port {port}...")
            else:
                print(f"\n❌ Error starting server: {e}")
                print("Please check if another application is using port 5000-5002")
                input("Press Enter to exit...")
                sys.exit(1)
        except ModuleNotFoundError:
            print("\n❌ Error: Could not find the app module")
            print("Make sure app.py is in the same directory as this script")
            print(f"Current directory: {os.getcwd()}")
            input("Press Enter to exit...")
            sys.exit(1)
        except Exception as e:
            print(f"\n❌ Error starting application: {e}")
            input("Press Enter to exit...")
            sys.exit(1)

if __name__ == "__main__":
    main()


