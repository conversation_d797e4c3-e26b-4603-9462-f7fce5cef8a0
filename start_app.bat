@echo off
REM QR ID Desktop Application Launcher for Windows
REM Double-click this file to start the application

title QR ID Desktop Application

echo ============================================================
echo    QR ID Desktop Application Launcher
echo ============================================================
echo.

REM Set working directory
cd /d "C:\ID_QR_PRINTING"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)

echo Python found - checking version...
python -c "import sys; exit(0 if sys.version_info >= (3,8) else 1)"
if errorlevel 1 (
    echo ERROR: Python 3.8 or higher is required
    python --version
    pause
    exit /b 1
)

echo Python version OK
echo.

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found in current directory.
    pause
    exit /b 1
)

REM Check if required dependencies are installed
echo Checking dependencies...
python -c "import flask, pandas, PIL, qrcode, cryptography, psutil, reportlab" >nul 2>&1
if errorlevel 1 (
    echo Some dependencies are missing. Installing...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies.
        pause
        exit /b 1
    )
)
echo Dependencies OK
echo.

REM Check and generate encryption key if missing
echo Checking encryption key...
if not exist "qr_key.key" (
    echo Encryption key not found. Generating new key...
    python generate_qr_key.py
)
echo Encryption key OK
echo.

REM Start the application
echo Starting QR ID Desktop Application...
echo The application will open in your default browser.
echo To stop the application, close this window or press Ctrl+C.
echo.

python start_app.py
if errorlevel 1 (
    echo.
    echo Application failed to start. Check the error message above.
    echo Common issues:
    echo - Port 5000 already in use by another application
    echo - Missing dependencies
    echo - Incorrect file paths
    echo.
    echo See TROUBLESHOOTING.md for more help.
    pause
    exit /b 1
)

echo.
echo Application stopped.
pause

