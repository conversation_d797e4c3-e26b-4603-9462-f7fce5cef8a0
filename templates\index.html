<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Smart QR ID Scanner</title>
    <link
      rel="icon"
      type="image/png"
      href="{{ url_for('static', filename='trademark/blurbgicon.png') }}"
    />

    <!-- External Libraries -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <!-- Custom Styles -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/index.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/button.css') }}"
    />
  </head>
  <body class="bg-light">
    <!-- Settings Modal -->
    <div id="settingsModal" class="modal-overlay" style="display: none">
      <div class="modal-content" style="width: 90%; max-width: 600px">
        <div id="activatePanel" class="p-4">
          <button class="close-btn" onclick="closeSettingsModal()">
            <i class="fas fa-times"></i>
          </button>

          <h4 class="mb-4 text-center">Configure Your Settings</h4>

          <!-- Dataset Section -->
          <div class="settings-section mb-4">
            <h3><i class="fas fa-database"></i> Dataset</h3>
            <div class="d-flex align-items-center gap-2 mt-2">
              <span id="current-dataset" class="text-truncate flex-grow-1">
                {% if active_dataset %}{{ active_dataset }}{% else %}No dataset
                selected{% endif %}
              </span>
              <button
                class="btn btn-sm btn-outline-primary"
                onclick="triggerFile('dataset')"
              >
                <i class="fas fa-upload"></i> Upload
              </button>
            </div>
            <input
              type="file"
              name="dataset_file"
              accept=".csv"
              class="d-none"
              id="datasetInput"
            />
          </div>

          <!-- Template Section -->
          <div class="settings-section mb-4">
            <h3><i class="fas fa-image"></i> Template</h3>
            <div class="d-flex align-items-center gap-2 mt-2">
              <span id="current-template" class="text-truncate flex-grow-1">
                {% if active_template %}{{ active_template }}{% else %}No
                template selected{% endif %}
              </span>
              <button
                class="btn btn-sm btn-outline-primary"
                onclick="triggerFile('template')"
              >
                <i class="fas fa-upload"></i> Upload
              </button>
            </div>
            <input
              type="file"
              name="template_file"
              accept=".png,.jpg,.jpeg,.gif,.bmp,.webp,.tiff,.tif"
              class="d-none"
              id="templateInput"
            />
          </div>

          <!-- Paper Size Section -->
          <div class="settings-section mb-4">
            <h3><i class="fas fa-print"></i> Paper Size</h3>
            <select id="paperSize" class="form-select">
              <option value="">Select paper size</option>
              <option value="A4">A4 (210 × 297 mm)</option>
              <option value="Letter">Letter (216 × 279 mm)</option>
              <option value="Legal">Legal (216 × 356 mm)</option>
              <option value="A3">A3 (297 × 420 mm)</option>
              <option value="A5">A5 (148 × 210 mm)</option>
              <option value="A6">A6 (105 × 148 mm)</option>
              <option value="custom">Custom Size</option>
            </select>

            <div
              id="customSizeFields"
              class="custom-size-inputs"
              style="display: none"
            >
              <input
                type="number"
                id="customWidth"
                placeholder="Width (inches)"
                step="0.01"
                class="form-input"
              />
              <input
                type="number"
                id="customHeight"
                placeholder="Height (inches)"
                step="0.01"
                class="form-input"
              />
            </div>
          </div>

          <div class="d-flex justify-content-end">
            <button class="btn btn-primary" onclick="submitSettings()">
              <i class="fas fa-check me-2"></i>Apply Settings
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Container -->
    <div class="container py-4">
      <!-- Success Alert -->
      {% if success %}
      <div
        id="successAlert"
        class="alert alert-success alert-dismissible fade show"
        role="alert"
      >
        <i class="fas fa-check-circle me-2"></i>Settings updated successfully!
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>
      {% endif %}

      <!-- Header with Settings Button -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
          <i class="fas fa-qrcode me-2 text-primary"></i>Smart QR ID Scanner
        </h1>
        <button class="settingbtn" onclick="openSettingsModal()">
          <div id="container-stars">
            <div id="stars"></div>
          </div>
          <div id="glow">
            <div class="circle"></div>
            <div class="circle"></div>
          </div>
          <strong><i class="fas fa-cog me-2"></i>Settings</strong>
        </button>
      </div>

      <!-- Main Content Grid -->
      <div class="row g-4">
        <!-- ID Template Preview -->
        <div class="col-lg-6">
          <div class="preview-section">
            <h5 class="section-title mb-3">
              <i class="fas fa-id-card me-2"></i>ID Template Preview
            </h5>

            <!-- Print Preview Option -->
            <div class="form-check mb-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="previewBeforePrint"
              />
              <label class="form-check-label" for="previewBeforePrint">
                <i class="fas fa-eye me-1"></i>Show print preview before
                printing
              </label>
            </div>

            <!-- Preview Container -->
            <div id="paperBoundary" class="preview-frame">
              <div id="scaledPreviewWrapper" class="preview-wrapper">
                <div id="idPreviewContainer" class="id-preview-container">
                  {% if active_template %}
                  <img
                    id="id-template-preview"
                    src="{{ url_for('static', filename='id_templates/' + active_template) }}"
                    alt="ID Template"
                    class="template-image"
                  />
                  {% else %}
                  <div
                    class="d-flex align-items-center justify-content-center h-100 text-muted"
                  >
                    <div class="text-center">
                      <i class="fas fa-image fa-3x mb-3"></i>
                      <p>No template selected</p>
                    </div>
                  </div>
                  {% endif %}

                  <div class="id-overlay">
                    <div id="preview-id" class="preview-id">000</div>
                    <div class="preview-spacer"></div>
                    <div id="preview-name" class="preview-name">
                      Sample Name
                    </div>
                    <div id="preview-position" class="preview-position">
                      Position
                    </div>
                    <div id="preview-company" class="preview-company">
                      Company
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Scanner and ID Info -->
        <div class="col-lg-6">
          <div class="scanner-section">
            <h5 class="section-title mb-3">
              <i class="fas fa-camera me-2"></i>QR Code Scanner
            </h5>

            <!-- Alert Box -->
            <div id="alertBox" class="alert d-none" role="alert"></div>

            <!-- Loading Spinner -->
            <div class="text-center mb-3">
              <div id="loadingSpinner" class="loading-spinner d-none">
                <div class="spinner">
                  <span></span>
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div class="mt-2 text-muted">Processing...</div>
              </div>
            </div>

            <!-- QR Reader -->
            <div class="scanner-container mb-4">
              <div
                id="reader"
                class="qr-reader"
                style="
                  width: 100%;
                  max-width: 350px;
                  aspect-ratio: 1;
                  margin: 0 auto;
                "
              ></div>
              <div class="scanner-status text-center mt-2">
                <small class="text-muted">
                  <i class="fas fa-info-circle me-1"></i>Point camera at QR code
                  to scan
                </small>
              </div>
            </div>

            <!-- ID Information Card -->
            <div class="id-info-card">
              <h6 class="mb-3">
                <i class="fas fa-user me-2"></i>Employee Information
              </h6>
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label"><i class="fas fa-hashtag"></i>ID</div>
                  <div class="info-value" id="id">-</div>
                </div>
                <div class="info-item">
                  <div class="info-label"><i class="fas fa-user"></i>Name</div>
                  <div class="info-value" id="name">-</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-briefcase"></i>Position
                  </div>
                  <div class="info-value" id="position">-</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-building"></i>Company
                  </div>
                  <div class="info-value" id="company">-</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bulk Operations Section -->
        <div class="row g-4 mt-2">
          <div class="col-12">
            <div class="bulk-actions">
              <h4><i class="fas fa-tasks me-2"></i>Bulk Operations</h4>

              <div class="action-buttons">
                <div class="row g-3">
                  <div class="col-md-6">
                    <button
                      class="btn btn-success w-100"
                      onclick="sendBulkEmails()"
                    >
                      <i class="fas fa-envelope me-2"></i>Send All QR Codes via
                      Email
                    </button>
                  </div>
                  <div class="col-md-6">
                    <button
                      class="btn btn-primary w-100"
                      onclick="downloadAllQRs()"
                    >
                      <i class="fas fa-download me-2"></i>Download All QR Codes
                    </button>
                  </div>
                </div>

                <div class="row g-3 mt-2">
                  <div class="col-md-6">
                    <button
                      class="btn btn-info w-100"
                      onclick="generateAllQRs()"
                    >
                      <i class="fas fa-qrcode me-2"></i>Generate Missing QR
                      Codes
                    </button>
                  </div>
                  <div class="col-md-6">
                    <button
                      class="btn btn-secondary w-100"
                      onclick="viewEmployeeList()"
                    >
                      <i class="fas fa-list me-2"></i>View Employee List
                    </button>
                  </div>
                </div>
              </div>

              <!-- Status indicators -->
              <div class="mt-3 d-flex gap-3 flex-wrap">
                <div class="status-indicator" id="emailStatus">
                  <i class="fas fa-envelope"></i>
                  <span>Email: <span id="emailStatusText">Ready</span></span>
                </div>
                <div class="status-indicator" id="qrStatus">
                  <i class="fas fa-qrcode"></i>
                  <span>QR Codes: <span id="qrStatusText">Ready</span></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
      // Global variables
      const alertBox = document.getElementById("alertBox");
      const spinner = document.getElementById("loadingSpinner");
      let html5QrCode = null;
      let scannerActive = false;
      let datasetFile = null;
      let templateFile = null;
      let settingsConfigured = false;

      // Paper size configurations (width, height in inches)
      const paperSizeMap = {
          A4: [8.27, 11.69],
          Letter: [8.5, 11],
          Legal: [8.5, 14],
          A3: [11.7, 16.5],
          A5: [5.8, 8.3],
          A6: [4.1, 5.8],
          custom: [3.375, 2.125] // Default custom size
      };

      // Initialize application
      document.addEventListener("DOMContentLoaded", function() {
          console.log("Initializing QR ID Scanner...");

          // Check if we have active dataset and template
          const hasDataset = "{{ active_dataset }}" !== "None" && "{{ active_dataset }}" !== "" && "{{ active_dataset }}" !== "null";
          const hasTemplate = "{{ active_template }}" !== "None" && "{{ active_template }}" !== "" && "{{ active_template }}" !== "null";

          console.log("Dataset:", "{{ active_dataset }}", "Template:", "{{ active_template }}");
          console.log("Has dataset:", hasDataset, "Has template:", hasTemplate);

          // Show first-run setup if no configuration exists
          if (!hasDataset || !hasTemplate) {
              console.log("Redirecting to first-run setup...");
              window.location.href = "/first-run";
              return;
          }

          settingsConfigured = true;

          // Setup all components
          setupPaperSizeControls();
          setupFileInputs();
          initializeScanner();
          initializeTemplateBackground();

          // Auto-hide success alert
          {% if success %}
          setTimeout(() => {
              const successAlert = document.getElementById("successAlert");
              if (successAlert) {
                  successAlert.remove();
              }
          }, 5000);
          {% endif %}
      });

      // Modal functions
      function openSettingsModal() {
          showSettingsModal();
      }

      function showSettingsModal() {
          document.getElementById("settingsModal").style.display = "flex";
      }

      function closeSettingsModal() {
          document.getElementById("settingsModal").style.display = "none";
      }

      function submitSettings() {
          const paperSize = document.getElementById("paperSize").value;

          if (!paperSize) {
              showAlert("Please select a paper size", "warning");
              return;
          }

          // Check if files need to be uploaded
          if (datasetFile || templateFile) {
              uploadFiles();
              return;
          }

          settingsConfigured = true;
          closeSettingsModal();
          initializeScanner();
          showAlert("Settings applied successfully!", "success");
      }

      // Camera and scanner functions
      function initializeScanner() {
          if (html5QrCode) {
              startScanner();
              return;
          }

          try {
              html5QrCode = new Html5Qrcode("reader");
              startScanner();
          } catch (error) {
              console.error("Failed to initialize QR scanner:", error);
              showAlert("Failed to initialize camera scanner", "danger");
          }
      }

      function startScanner() {
          if (scannerActive || !settingsConfigured) return;

          scannerActive = true;

          // Get reader element dimensions
          const readerElement = document.getElementById('reader');
          const readerWidth = readerElement ? readerElement.clientWidth : 350;

          // Make QR box square and slightly smaller than container
          const qrboxSize = Math.min(readerWidth, 350) * 0.8;

          const config = {
              fps: 10,
              qrbox: { width: qrboxSize, height: qrboxSize },
              aspectRatio: 1.0,
              formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE]
          };

          html5QrCode.start(
              { facingMode: "environment" },
              config,
              (decodedText) => {
                  handleQRScan(decodedText.trim());
              },
              (errorMessage) => {
                  // Ignore frequent scanning errors
                  if (!errorMessage.includes("No QR code found")) {
                      console.warn("QR Scanner:", errorMessage);
                  }
              }
          ).catch((err) => {
              scannerActive = false;
              console.error("Camera start error:", err);
              showAlert("Camera access denied or not available", "warning");
          });
      }

      function stopScanner() {
          if (html5QrCode && scannerActive) {
              html5QrCode.stop().then(() => {
                  scannerActive = false;
              }).catch(err => {
                  console.error("Error stopping scanner:", err);
                  scannerActive = false;
              });
          }
      }

      function handleQRScan(qrContent) {
          if (!qrContent) return;

          stopScanner();
          fetchEmployeeData(qrContent);
      }

      // Data handling functions
      function clearEmployeeFields() {
          const fields = ["id", "name", "position", "company"];
          fields.forEach(field => {
              const element = document.getElementById(field);
              if (element) element.textContent = "-";
          });

          // Clear preview fields
          document.getElementById("preview-id").textContent = "000";
          document.getElementById("preview-name").textContent = "Sample Name";
          document.getElementById("preview-position").textContent = "Position";
          document.getElementById("preview-company").textContent = "Company";
      }

      function fetchEmployeeData(qrContent) {
          clearEmployeeFields();
          showSpinner(true);

          fetch("/get_data", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ qr_content: qrContent })
          })
          .then(response => response.json())
          .then(data => {
              showSpinner(false);

              if (data.error || !data.success) {
                  showAlert("❌ Employee not found or invalid QR code", "danger");
                  setTimeout(() => startScanner(), 3000);
                  return;
              }

              // Update employee information display
              updateEmployeeDisplay(data);

              // Show success message
              showAlert("✅ Employee found! Preparing to print...", "success");

              // Auto-print after short delay
              setTimeout(() => {
                  printEmployeeID(data);
              }, 1500);
          })
          .catch(error => {
              showSpinner(false);
              console.error("Fetch error:", error);
              showAlert("❌ Network error: " + error.message, "danger");
              setTimeout(() => startScanner(), 3000);
          });
      }

      function updateEmployeeDisplay(data) {
          // Update info display
          document.getElementById("id").textContent = data.ID || "-";
          document.getElementById("name").textContent = data.Name || "-";
          document.getElementById("position").textContent = data.Position || "-";
          document.getElementById("company").textContent = data.Company || "-";

          // Update preview overlay
          document.getElementById("preview-id").textContent = String(data.ID || "000").padStart(3, "0");
          document.getElementById("preview-name").textContent = data.Name || "Sample Name";
          document.getElementById("preview-position").textContent = data.Position || "Position";
          document.getElementById("preview-company").textContent = data.Company || "Company";
      }

      // Printing functions
      function printEmployeeID(employeeData) {
          const preview = document.getElementById("idPreviewContainer");
          const wrapper = document.getElementById("scaledPreviewWrapper");
          const paperSize = document.getElementById("paperSize").value || "A4";
          const showPreview = document.getElementById("previewBeforePrint").checked;

          if (!preview) {
              showAlert("❌ Preview container not found", "danger");
              resetToScanning();
              return;
          }

          // Get paper dimensions
          let customWidth = 3.375;
          let customHeight = 2.125;

          if (paperSize === "custom") {
              customWidth = parseFloat(document.getElementById("customWidth").value) || 3.375;
              customHeight = parseFloat(document.getElementById("customHeight").value) || 2.125;
          }

          // Prepare for high-quality capture
          const originalTransform = wrapper.style.transform;
          wrapper.style.transform = "scale(1)";

          // Capture the preview as image
          html2canvas(preview, {
              scale: 3, // High DPI for printing
              useCORS: true,
              allowTaint: true,
              backgroundColor: "#ffffff",
              logging: false
          }).then(canvas => {
              // Restore original transform
              wrapper.style.transform = originalTransform;

              const imageData = canvas.toDataURL("image/png");

              const printPayload = {
                  image_base64: imageData,
                  paper_size: paperSize,
                  custom_width: customWidth,
                  custom_height: customHeight,
                  redirect: showPreview
              };

              // Send to print endpoint
              fetch("/print_image_direct", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify(printPayload)
              })
              .then(response => response.json())
              .then(result => {
                  if (result.success) {
                      if (showPreview && result.preview_url) {
                          // Redirect to preview page
                          window.location.href = result.preview_url;
                      } else {
                          // Print successful, show message and reset
                          showAlert("✅ Print job sent successfully!", "success");
                          setTimeout(() => resetToScanning(), 3000);
                      }
                  } else {
                      showAlert("❌ Print failed: " + (result.error || "Unknown error"), "danger");
                      resetToScanning();
                  }
              })
              .catch(error => {
                  console.error("Print error:", error);
                  showAlert("❌ Print request failed: " + error.message, "danger");
                  resetToScanning();
              });
          }).catch(error => {
              wrapper.style.transform = originalTransform;
              console.error("Canvas capture error:", error);
              showAlert("❌ Failed to capture preview", "danger");
              resetToScanning();
          });
      }

      function resetToScanning() {
          clearEmployeeFields();
          setTimeout(() => {
              if (settingsConfigured) {
                  startScanner();
              }
          }, 2000);
      }

      // File handling functions
      function triggerFile(type) {
          const input = document.getElementById(`${type}Input`);
          if (input) {
              input.click();
          }
      }

      function setupFileInputs() {
          // Dataset file input
          const datasetInput = document.getElementById("datasetInput");
          if (datasetInput) {
              datasetInput.addEventListener("change", function(e) {
                  const file = e.target.files[0];
                  if (file) {
                      datasetFile = file;
                      document.getElementById("current-dataset").textContent = file.name;
                      console.log("Dataset file selected:", file.name);
                  }
              });
          }

          // Template file input
          const templateInput = document.getElementById("templateInput");
          if (templateInput) {
              templateInput.addEventListener("change", function(e) {
                  const file = e.target.files[0];
                  if (file) {
                      templateFile = file;
                      document.getElementById("current-template").textContent = file.name;
                      console.log("Template file selected:", file.name);
                  }
              });
          }
      }

      function uploadFiles() {
          if (!datasetFile && !templateFile) {
              showAlert("Please select files to upload", "warning");
              return;
          }

          const formData = new FormData();
          if (datasetFile) {
              formData.append("dataset_file", datasetFile);
          }
          if (templateFile) {
              formData.append("template_file", templateFile);
          }
          formData.append("triggeredBy", "manual");

          showSpinner(true);
          showAlert("Uploading files...", "info");

          fetch("/activate", {
              method: "POST",
              body: formData
          })
          .then(response => {
              if (response.redirected) {
                  window.location.href = response.url;
                  return;
              }
              return response.json();
          })
          .then(data => {
              showSpinner(false);
              if (data && data.success) {
                  showAlert("Files uploaded successfully!", "success");
                  setTimeout(() => {
                      window.location.reload();
                  }, 1500);
              } else {
                  showAlert("Upload failed: " + (data?.error || "Unknown error"), "danger");
              }
          })
          .catch(error => {
              showSpinner(false);
              console.error("Upload error:", error);
              showAlert("Upload failed: " + error.message, "danger");
          });
      }

      // Paper size and preview functions
      function setupPaperSizeControls() {
          const paperSelect = document.getElementById("paperSize");
          const customFields = document.getElementById("customSizeFields");

          if (!paperSelect || !customFields) return;

          paperSelect.addEventListener("change", function() {
              if (this.value === "custom") {
                  customFields.style.display = "grid";
              } else {
                  customFields.style.display = "none";
                  if (this.value) {
                      updatePreviewForPaperSize(this.value);
                  }
              }
          });

          // Custom size inputs
          const customWidth = document.getElementById("customWidth");
          const customHeight = document.getElementById("customHeight");

          if (customWidth && customHeight) {
              customWidth.addEventListener("input", applyCustomSize);
              customHeight.addEventListener("input", applyCustomSize);
          }

          // Set default paper size
          paperSelect.value = "A4";
          updatePreviewForPaperSize("A4");
      }

      function updatePreviewForPaperSize(paperSize) {
          const dimensions = paperSizeMap[paperSize];
          if (dimensions) {
              updatePreviewSize(dimensions[0], dimensions[1]);
          }
      }

      function applyCustomSize() {
          const width = parseFloat(document.getElementById("customWidth").value);
          const height = parseFloat(document.getElementById("customHeight").value);

          if (width > 0 && height > 0) {
              updatePreviewSize(width, height);
          }
      }

      function updatePreviewSize(widthInches, heightInches) {
          const preview = document.getElementById("idPreviewContainer");
          const wrapper = document.getElementById("scaledPreviewWrapper");

          if (!preview || !wrapper) return;

          // Calculate dimensions for preview
          const maxPreviewSize = 400; // Max size for preview container
          const aspectRatio = widthInches / heightInches;

          let previewWidth, previewHeight;

          if (aspectRatio > 1) {
              // Landscape
              previewWidth = Math.min(maxPreviewSize, widthInches * 50);
              previewHeight = previewWidth / aspectRatio;
          } else {
              // Portrait
              previewHeight = Math.min(maxPreviewSize, heightInches * 50);
              previewWidth = previewHeight * aspectRatio;
          }

          // Apply dimensions
          preview.style.width = `${previewWidth}px`;
          preview.style.height = `${previewHeight}px`;

          // Scale wrapper to fit in container
          const containerSize = 400;
          const scale = Math.min(containerSize / previewWidth, containerSize / previewHeight, 1);
          wrapper.style.transform = `scale(${scale})`;

          console.log(`Preview updated: ${widthInches}"x${heightInches}" -> ${previewWidth}x${previewHeight}px (scale: ${scale})`);
      }

      // Utility functions
      function showAlert(message, type = "info") {
          if (!alertBox) return;

          alertBox.className = `alert alert-${type} d-flex align-items-center`;
          alertBox.innerHTML = `
              <i class="fas fa-${getAlertIcon(type)} me-2"></i>
              <span>${message}</span>
          `;
          alertBox.classList.remove("d-none");

          // Auto-hide after 5 seconds
          setTimeout(() => {
              alertBox.classList.add("d-none");
          }, 5000);
      }

      function getAlertIcon(type) {
          const icons = {
              success: "check-circle",
              danger: "exclamation-triangle",
              warning: "exclamation-circle",
              info: "info-circle"
          };
          return icons[type] || "info-circle";
      }

      function showSpinner(show) {
          if (!spinner) return;

          if (show) {
              spinner.classList.remove("d-none");
          } else {
              spinner.classList.add("d-none");
          }
      }

      // Initialize template background effect
      function initializeTemplateBackground() {
          const templateImg = document.getElementById("id-template-preview");
          if (templateImg && templateImg.complete) {
              setTemplateBackground(templateImg);
          } else if (templateImg) {
              templateImg.onload = function() {
                  setTemplateBackground(this);
              };
          }
      }

      function setTemplateBackground(img) {
          try {
              const canvas = document.createElement("canvas");
              const context = canvas.getContext("2d");
              canvas.width = 1;
              canvas.height = 1;
              context.drawImage(img, 0, 0, 1, 1);
              const [r, g, b] = context.getImageData(0, 0, 1, 1).data;
              const color = `rgb(${r}, ${g}, ${b})`;

              const paperBoundary = document.getElementById("paperBoundary");
              if (paperBoundary) {
                  paperBoundary.style.background = `radial-gradient(circle, ${color}22, ${color}11)`;
              }
          } catch (error) {
              console.warn("Could not set template background:", error);
          }
      }

      // Initialize template background when DOM is ready
      setTimeout(initializeTemplateBackground, 500);

      // Bulk operation functions
      function sendBulkEmails() {
          if (!confirm("Send QR codes to all employees via email? This may take a few minutes.")) {
              return;
          }

          showSpinner(true);
          showAlert("Sending emails to all employees...", "info");

          fetch("/send_qr_emails", {
              method: "POST",
              headers: { "Content-Type": "application/json" }
          })
          .then(response => response.json())
          .then(data => {
              showSpinner(false);
              if (data.success) {
                  showAlert(`✅ Emails sent successfully! Sent: ${data.sent}, Failed: ${data.failed}, No Email: ${data.no_email}`, "success");
                  updateEmailStatus("online");
              } else {
                  showAlert("❌ " + (data.error || "Failed to send emails"), "danger");
                  updateEmailStatus("offline");
              }
          })
          .catch(error => {
              showSpinner(false);
              console.error("Email error:", error);
              showAlert("❌ Network error: " + error.message, "danger");
              updateEmailStatus("offline");
          });
      }

      function downloadAllQRs() {
          showSpinner(true);
          showAlert("Preparing QR codes for download...", "info");

          // Create a temporary link to trigger download
          const link = document.createElement('a');
          link.href = '/download_all_qrs';
          link.download = 'All_QR_Codes.zip';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          setTimeout(() => {
              showSpinner(false);
              showAlert("✅ QR codes download started!", "success");
          }, 2000);
      }

      function generateAllQRs() {
          if (!confirm("Generate QR codes for all employees? This may take a few minutes.")) {
              return;
          }

          showSpinner(true);
          showAlert("Generating missing QR codes...", "info");
          updateQRStatus("processing");

          fetch("/start_background_qr_generation", {
              method: "POST",
              headers: { "Content-Type": "application/json" }
          })
          .then(response => response.json())
          .then(data => {
              showSpinner(false);
              if (data.success) {
                  showAlert(`✅ QR generation started! ${data.missing_qrs || 0} QR codes will be generated.`, "success");
                  updateQRStatus("online");
              } else {
                  showAlert("❌ " + (data.error || "Failed to start QR generation"), "danger");
                  updateQRStatus("offline");
              }
          })
          .catch(error => {
              showSpinner(false);
              console.error("QR generation error:", error);
              showAlert("❌ Network error: " + error.message, "danger");
              updateQRStatus("offline");
          });
      }

      function viewEmployeeList() {
          fetch("/employees")
          .then(response => response.json())
          .then(data => {
              if (data.success && data.employees) {
                  const employees = data.employees;
                  let listHtml = `
                      <div class="modal fade" id="employeeModal" tabindex="-1">
                          <div class="modal-dialog modal-lg">
                              <div class="modal-content">
                                  <div class="modal-header">
                                      <h5 class="modal-title">Employee List (${employees.length} employees)</h5>
                                      <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                  </div>
                                  <div class="modal-body">
                                      <div class="table-responsive">
                                          <table class="table table-striped">
                                              <thead>
                                                  <tr>
                                                      <th>ID</th>
                                                      <th>Name</th>
                                                      <th>Position</th>
                                                      <th>Company</th>
                                                      <th>Actions</th>
                                                  </tr>
                                              </thead>
                                              <tbody>
                  `;

                  employees.forEach(emp => {
                      listHtml += `
                          <tr>
                              <td>${emp.ID}</td>
                              <td>${emp.Name}</td>
                              <td>${emp.Position}</td>
                              <td>${emp.Company}</td>
                              <td>
                                  <button class="btn btn-sm btn-primary" onclick="downloadSingleQR('${emp.ID}')">
                                      <i class="fas fa-download"></i>
                                  </button>
                              </td>
                          </tr>
                      `;
                  });

                  listHtml += `
                                              </tbody>
                                          </table>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  `;

                  // Remove existing modal if any
                  const existingModal = document.getElementById('employeeModal');
                  if (existingModal) {
                      existingModal.remove();
                  }

                  // Add new modal to body
                  document.body.insertAdjacentHTML('beforeend', listHtml);

                  // Show modal
                  const modal = new bootstrap.Modal(document.getElementById('employeeModal'));
                  modal.show();
              } else {
                  showAlert("❌ Failed to load employee list", "danger");
              }
          })
          .catch(error => {
              console.error("Employee list error:", error);
              showAlert("❌ Network error: " + error.message, "danger");
          });
      }

      function downloadSingleQR(employeeId) {
          const link = document.createElement('a');
          link.href = `/download_qr/${employeeId}`;
          link.download = `QR_${employeeId}.png`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
      }

      function updateEmailStatus(status) {
          const statusElement = document.getElementById("emailStatus");
          const textElement = document.getElementById("emailStatusText");

          if (statusElement && textElement) {
              statusElement.className = `status-indicator ${status}`;
              textElement.textContent = status === "online" ? "Ready" : status === "offline" ? "Error" : "Processing";
          }
      }

      function updateQRStatus(status) {
          const statusElement = document.getElementById("qrStatus");
          const textElement = document.getElementById("qrStatusText");

          if (statusElement && textElement) {
              statusElement.className = `status-indicator ${status}`;
              textElement.textContent = status === "online" ? "Ready" : status === "offline" ? "Error" : "Processing";
          }
      }
    </script>
  </body>
</html>
