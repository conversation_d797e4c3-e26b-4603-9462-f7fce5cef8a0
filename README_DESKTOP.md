# QR ID Desktop Application

A local desktop application for QR code generation, ID card printing, and employee data management. This application runs entirely on your local machine and only requires internet connectivity for email functionality.

## 🚀 Quick Start

### Windows Users

1. **Double-click `start_app.bat`** - This will automatically check dependencies and start the application
2. The application will open in your default browser at `http://localhost:5000`

### Linux/Mac Users

1. **Run `./start_app.sh`** in terminal - This will check dependencies and start the application
2. The application will open in your default browser at `http://localhost:5000`

### Alternative Start Methods

- **Python directly**: `python start_app.py`
- **Flask app directly**: `python app.py`

## 📋 Requirements

- **Python 3.8 or higher**
- **Dependencies** (automatically installed by launcher scripts):
  - Flask (web framework)
  - Pandas (data processing)
  - Pillow (image processing)
  - QRCode (QR code generation)
  - Cryptography (data encryption)
  - psutil (system monitoring)
  - ReportLab (PDF generation)
  - Flask-Mail (email support - optional)

## 🛠️ Manual Installation

If the automatic launchers don't work, install manually:

```bash
# Install Python dependencies
pip install -r requirements.txt

# Start the application
python start_app.py
```

## ✨ Features

### Core Features (No Internet Required)

- 📱 **QR Code Generation** - Create QR codes from CSV data
- 🖨️ **ID Card Printing** - Print professional ID cards with QR codes
- 📊 **CSV Data Import** - Import employee data from CSV files
- 🖼️ **Template Management** - Upload and manage ID card templates
- 📷 **QR Code Scanning** - Scan and decode QR codes using camera
- 💾 **Local File Management** - All data stored locally on your machine

### Email Features (Internet Required)

- 📧 **Email QR Codes** - Send QR codes directly to employee email addresses
- 📬 **Bulk Email** - Send QR codes to multiple employees at once

## 📁 Directory Structure

```
ID_QR_PRINTING/
├── app.py                 # Main application
├── start_app.py          # Desktop launcher
├── start_app.bat         # Windows launcher
├── start_app.sh          # Linux/Mac launcher
├── create_favicon.py     # Favicon generator script
├── requirements.txt      # Python dependencies
├── .env                  # Configuration (auto-created)
├── TROUBLESHOOTING.md    # Detailed troubleshooting guide
├── static/
│   ├── qr_codes/        # Generated QR codes
│   ├── id_templates/    # ID card templates
│   ├── trademark/       # App icons and branding
│   │   ├── blurbgicon.png  # Application icon (PNG)
│   │   └── blurbgicon.ico  # Favicon (ICO format)
│   ├── css/             # Stylesheets
│   └── js/              # JavaScript files
├── templates/           # HTML templates
├── participant_list/    # CSV data files
└── README_DESKTOP.md   # This file
```

## ⚙️ Configuration

The application creates a `.env` file automatically with default settings. You can edit this file to customize:

### Email Configuration (Optional)

To enable email features, uncomment and configure these settings in `.env`:

```env
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>
```

### Memory Settings

```env
MEMORY_LIMIT_MB=1024  # Adjust based on your system
```

### File Management

The application automatically manages old files to keep your system clean:

- **Templates**: Keeps the current template + 3 most recent templates
- **Datasets**: Keeps the current dataset + 5 most recent datasets
- **QR Codes**: Cleaned up when datasets change
- **Cleanup Schedule**: Runs every 24 hours automatically

## 📝 Usage Instructions

### 1. First Time Setup

1. Start the application using one of the launcher scripts
2. Upload a CSV file with employee data (columns: ID, Name, Position, Company, Email)
3. Upload an ID card template image (PNG, JPG, etc.)
4. Click "Activate Configuration" to process the data

### 2. CSV File Format

Your CSV file should have these columns:

```csv
ID,Name,Position,Company,Email
001,John Doe,Manager,ABC Corp,<EMAIL>
002,Jane Smith,Developer,ABC Corp,<EMAIL>
```

### 3. Generating QR Codes

- QR codes are generated automatically when you activate a configuration
- Individual QR codes can be generated on-demand
- QR codes contain encrypted employee information

### 4. Printing ID Cards

- Use the camera scanner to scan employee QR codes
- The system will automatically format and print ID cards
- Supports various ID card templates and layouts

### 5. Email Features

- Configure email settings in `.env` file
- Send individual QR codes or bulk email to all employees
- Requires internet connection only for email functionality

## 🔧 Troubleshooting

📖 **For detailed troubleshooting information, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).**

### Quick Fixes for Common Issues

#### File Access Errors

- **"File is locked by another process"** → Close Excel/LibreOffice and try again
- **"Permission denied"** → Run as administrator or check file permissions

#### Application Issues

- **Won't start** → Check Python version (`python --version` must be 3.8+)
- **Dependencies missing** → Run `pip install -r requirements.txt`
- **Port 5000 in use** → Close other applications or restart computer

#### CSV File Issues

- **Invalid format** → Ensure columns: ID, Name, Position, Company
- **File too large** → Keep under 50MB and 10,000 rows
- **Special characters** → Save as "CSV UTF-8" format

#### Memory Issues

- **System memory low** → Close other applications
- **Processing fails** → Use smaller CSV files

### Complete Troubleshooting Guide

📖 **See [TROUBLESHOOTING.md](TROUBLESHOOTING.md) for detailed solutions to all common issues.**

## 🔒 Security & Privacy

- **Local Data**: All data is stored locally on your machine
- **Encryption**: QR codes contain encrypted employee information
- **No Cloud**: No data is sent to external servers (except for email)
- **Privacy**: Employee data remains on your local system

## 🆘 Support

If you encounter issues:

1. **Check the console output** for error messages
2. **Verify all requirements** are installed correctly
3. **Check file permissions** in the application directory
4. **Try running with administrator privileges** if needed

## 🎯 Key Differences from Web Version

This desktop version:

- ✅ Runs entirely locally (no cloud deployment)
- ✅ Uses local directories and file storage
- ✅ More generous memory and file size limits
- ✅ Simplified configuration
- ✅ No external dependencies except for email
- ✅ Better suited for offline use
- ✅ Direct file system access

---

**Enjoy using your local QR ID Desktop Application! 🎉**
