#!/bin/bash
# QR ID Desktop Application Launcher for Linux/Mac
# Run this script to start the application

set -e

echo "============================================================"
echo "    QR ID Desktop Application Launcher"
echo "============================================================"
echo

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ ERROR: Python 3 is not installed or not in PATH"
    echo
    echo "Please install Python 3.8 or higher:"
    echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "  macOS: brew install python3"
    echo "  Or download from: https://www.python.org/downloads/"
    exit 1
fi

echo "✓ Python found - checking version..."

# Check Python version
python3 -c "import sys; exit(0 if sys.version_info >= (3,8) else 1)" || {
    echo "❌ ERROR: Python 3.8 or higher is required"
    python3 --version
    exit 1
}

echo "✓ Python version OK"
echo

# Check if pip is available
if ! command -v pip3 &> /dev/null; then
    echo "❌ ERROR: pip3 is not installed"
    echo "Please install pip3 or use your system's package manager"
    exit 1
fi

# Check if requirements are installed
echo "🔍 Checking dependencies..."
python3 -c "import flask, pandas, PIL, qrcode, cryptography, psutil, reportlab" 2>/dev/null || {
    echo "📦 Some dependencies are missing. Installing..."
    echo
    pip3 install -r requirements.txt || {
        echo "❌ ERROR: Failed to install dependencies"
        echo "Please run: pip3 install -r requirements.txt"
        exit 1
    }
}

echo "✓ Dependencies OK"
echo

# Make sure the script is executable
chmod +x "$0"

# Start the application
echo "🚀 Starting QR ID Desktop Application..."
echo
echo "🌐 The application will open in your default browser"
echo "💡 To stop the application, press Ctrl+C in this terminal"
echo

python3 start_app.py
