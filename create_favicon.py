#!/usr/bin/env python3
"""
Create favicon.ico from the existing blurbgicon.png
This script converts the PNG icon to ICO format for better browser compatibility.
"""

import os
from PIL import Image

def create_favicon():
    """Convert PNG to ICO format for favicon"""
    
    # Paths
    png_path = os.path.join("static", "trademark", "blurbgicon.png")
    ico_path = os.path.join("static", "trademark", "blurbgicon.ico")
    
    try:
        # Check if PNG exists
        if not os.path.exists(png_path):
            print(f"❌ Error: {png_path} not found")
            return False
            
        # Open the PNG image
        print(f"📖 Opening {png_path}...")
        img = Image.open(png_path)
        
        # Convert to RGBA if not already
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Create multiple sizes for ICO (16x16, 32x32, 48x48)
        sizes = [(16, 16), (32, 32), (48, 48)]
        
        # Resize and save as ICO
        print(f"🔄 Converting to ICO format with sizes: {sizes}...")
        img.save(ico_path, format='ICO', sizes=sizes)
        
        print(f"✅ Successfully created {ico_path}")
        print(f"📏 Icon sizes: {sizes}")
        
        # Verify the file was created
        if os.path.exists(ico_path):
            file_size = os.path.getsize(ico_path)
            print(f"📊 File size: {file_size} bytes")
            return True
        else:
            print("❌ Error: ICO file was not created")
            return False
            
    except Exception as e:
        print(f"❌ Error creating favicon: {e}")
        return False

def main():
    """Main function"""
    print("=" * 50)
    print("🎨 Favicon Creator for QR ID Desktop App")
    print("=" * 50)

    # Check if ICO already exists
    ico_path = os.path.join("static", "trademark", "blurbgicon.ico")
    if os.path.exists(ico_path):
        print("✅ Favicon already exists!")
        print(f"📁 Location: {ico_path}")
        file_size = os.path.getsize(ico_path)
        print(f"📊 File size: {file_size} bytes")
        print("💡 No action needed - favicon is ready to use")
        return

    if create_favicon():
        print("\n🎉 Favicon creation successful!")
        print("💡 The application will now use the ICO format favicon")
        print("🔄 Restart the application to see the new favicon")
    else:
        print("\n⚠️  Favicon creation failed")
        print("💡 The application will use the PNG format as fallback")

    print("=" * 50)

if __name__ == "__main__":
    main()
