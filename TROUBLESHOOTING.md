# QR ID Desktop Application - Troubleshooting Guide

## 🔧 Common Issues and Solutions

### 📁 File Access Issues

#### Problem: "File is locked by another process" Error
**Error Message:** `[WinError 32] The process cannot access the file because it is being used by another process`

**Cause:** The CSV file is currently open in Excel, LibreOffice, or another application.

**Solutions:**
1. **Close Excel/Spreadsheet Applications**
   - Close Microsoft Excel completely
   - Close LibreOffice Calc or other spreadsheet programs
   - Check the system tray for hidden Excel processes

2. **Check Task Manager (Windows)**
   - Press `Ctrl + Shift + Esc` to open Task Manager
   - Look for `EXCEL.EXE` or `soffice.exe` processes
   - End these processes if found

3. **Use a Different File**
   - Save your CSV with a different filename
   - Try uploading the new file

4. **Restart the Application**
   - Close the QR ID application
   - Wait 10 seconds
   - Restart using `start_app.bat` or `python start_app.py`

#### Problem: "Permission Denied" Error
**Error Message:** `[WinError 5] Access is denied`

**Solutions:**
1. **Run as Administrator**
   - Right-click on `start_app.bat`
   - Select "Run as administrator"

2. **Check File Permissions**
   - Right-click on your CSV file
   - Select "Properties" → "Security"
   - Ensure you have "Full control" permissions

3. **Move Files to Desktop**
   - Copy your CSV file to the Desktop
   - Try uploading from there

### 💾 Memory Issues

#### Problem: "System memory low" Error
**Solutions:**
1. **Close Other Applications**
   - Close web browsers with many tabs
   - Close video/photo editing software
   - Close games or other memory-intensive programs

2. **Restart Your Computer**
   - Save your work
   - Restart the computer
   - Try again with fewer applications running

3. **Use Smaller Files**
   - Limit CSV files to under 10,000 rows
   - Remove unnecessary columns from your CSV

### 🌐 Browser Issues

#### Problem: Application Won't Open in Browser
**Solutions:**
1. **Manual Browser Opening**
   - Open your web browser
   - Go to: `http://localhost:5000`

2. **Try Different Browser**
   - Try Chrome, Firefox, or Edge
   - Disable browser extensions temporarily

3. **Check Firewall**
   - Temporarily disable Windows Firewall
   - Add exception for Python/Flask

### 📊 CSV File Issues

#### Problem: "Invalid CSV format" Error
**Solutions:**
1. **Check Required Columns**
   - Your CSV must have: `ID`, `Name`, `Position`, `Company`
   - Optional: `Email` (for email features)

2. **Fix CSV Format**
   ```csv
   ID,Name,Position,Company,Email
   001,John Doe,Manager,ABC Corp,<EMAIL>
   002,Jane Smith,Developer,ABC Corp,<EMAIL>
   ```

3. **Save as CSV UTF-8**
   - In Excel: File → Save As → CSV UTF-8 (Comma delimited)
   - Avoid special characters in the first row

#### Problem: "File too large" Error
**Solutions:**
1. **Split Large Files**
   - Break files into smaller chunks (under 50MB)
   - Process in batches

2. **Remove Unnecessary Data**
   - Delete empty rows/columns
   - Remove formatting and formulas

### 🖨️ Printing Issues

#### Problem: QR Codes Not Printing
**Solutions:**
1. **Check Printer Connection**
   - Ensure printer is connected and powered on
   - Print a test page from Windows

2. **Update Printer Drivers**
   - Download latest drivers from manufacturer
   - Restart after installation

3. **Try Different Format**
   - Use "Download QR Codes" instead of direct printing
   - Print the downloaded images manually

### 📧 Email Issues

#### Problem: Email Not Sending
**Solutions:**
1. **Check Internet Connection**
   - Email features require internet
   - Test your connection

2. **Configure Email Settings**
   - Edit the `.env` file
   - Add your email server settings:
   ```env
   MAIL_SERVER=smtp.gmail.com
   MAIL_PORT=587
   MAIL_USE_TLS=true
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-app-password
   ```

3. **Use App Passwords (Gmail)**
   - Enable 2-factor authentication
   - Generate an app-specific password
   - Use the app password instead of your regular password

### 🔄 Application Issues

#### Problem: Application Won't Start
**Solutions:**
1. **Check Python Installation**
   ```bash
   python --version
   ```
   - Should be Python 3.8 or higher

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Try Direct Start**
   ```bash
   python app.py
   ```

#### Problem: "Port 5000 already in use"
**Solutions:**
1. **Kill Existing Process**
   - Press `Ctrl + C` in any open terminals
   - Check Task Manager for Python processes

2. **Use Different Port**
   - Edit `start_app.py`
   - Change `port=5000` to `port=5001`

### 🆘 Getting Help

If none of these solutions work:

1. **Check the Console Output**
   - Look for error messages in the terminal/command prompt
   - Note the exact error text

2. **Restart Everything**
   - Close the application
   - Restart your computer
   - Try again

3. **Use Safe Mode**
   - Start with a small test CSV file (5-10 rows)
   - Use simple filenames without spaces or special characters

4. **Check System Requirements**
   - Windows 10/11 or modern Linux/macOS
   - At least 4GB RAM
   - 1GB free disk space
   - Python 3.8+

---

## 📝 Quick Checklist

Before reporting issues, please verify:

- [ ] CSV file is not open in Excel or other applications
- [ ] File has required columns (ID, Name, Position, Company)
- [ ] File size is under 50MB
- [ ] Python 3.8+ is installed
- [ ] All dependencies are installed (`pip install -r requirements.txt`)
- [ ] No other applications are using port 5000
- [ ] You have sufficient disk space and memory

---

**Remember:** This is a local desktop application. All your data stays on your computer, and internet is only needed for email features.
